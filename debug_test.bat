@echo off
echo DEBUG TEST - <PERSON><PERSON><PERSON> tra từng bước
echo ================================

echo.
echo === Compiling ===
g++ -o SUM.exe init_code\SUM.CPP
g++ -o GIAONHAU.exe init_code\GIAONHAU.CPP
g++ -o CHIEUSANG.exe init_code\CHIEUSANG.cpp

if errorlevel 1 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Compilation successful!

echo.
echo === Testing SUM ===
echo Test 1: [2,3,1,5,4], k=7 (Expected: 2)
copy tests\sum_normal.inp SUM.INP
SUM.exe
echo Got:
type SUM.OUT
echo.

echo Test 2: [3,3,3,1,5,0], k=6 (Expected: 3)  
copy tests\sum_duplicates.inp SUM.INP
SUM.exe
echo Got:
type SUM.OUT
echo.

echo Test 3: [5,5,5,5,5], k=10 (Expected: 10)
copy tests\sum_all_same.inp SUM.INP
SUM.exe
echo Got:
type SUM.OUT
echo.

echo === Testing GIAONHAU ===
echo Test 1: [1,2,2,1] giao [2,2] (Expected: 2)
copy tests\giao_normal.inp GIAONHAU.INP
GIAONHAU.exe
echo Got:
type GIAONHAU.OUT
echo.

echo Test 2: [1,2,3] giao [4,5,6] (Expected: empty)
copy tests\giao_empty.inp GIAONHAU.INP
GIAONHAU.exe
echo Got:
type GIAONHAU.OUT
echo "(should be empty)"
echo.

echo === Testing CHIEUSANG ===
echo Test 1: nhà [-2,2,4], đèn [-3,0] (Expected: 4)
copy tests\chieu_normal.inp CHIEUSANG.INP
CHIEUSANG.exe
echo Got:
type CHIEUSANG.OUT
echo.

echo Test 2: nhà [-1000000000], đèn [1000000000] (Expected: 2000000000)
copy tests\chieu_max.inp CHIEUSANG.INP
CHIEUSANG.exe
echo Got:
type CHIEUSANG.OUT
echo.

echo === Cleanup ===
del *.exe *.INP *.OUT 2>nul

echo.
echo Debug test completed!
pause

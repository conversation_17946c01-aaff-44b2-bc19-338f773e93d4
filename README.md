# DSA Lab Test Suite

Bộ test tự động cho 3 bài tập DSA Lab.

## C<PERSON>u trúc thư mục
```
DSA/
├── init_code/           # Code nguồn
│   ├── SUM.CPP         # Bài 1: <PERSON><PERSON><PERSON> tổng
│   ├── GIAONHAU.CPP    # Bài 2: <PERSON><PERSON><PERSON> <PERSON>
│   ├── CHIEUSANG.cpp   # Bài 3: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> sáng
│   └── *.INP           # File input mẫu
├── test_runner.py      # Script test chính
├── test.bat           # Script Windows
├── test.sh            # Script Linux/Mac
├── Makefile           # Build script
└── README.md          # Hướng dẫn này
```

## Cách chạy test

### Windows
```cmd
# Test tất cả bài
test.bat

# Test bài cụ thể
test.bat 1    # Chỉ test bài 1
test.bat 2    # Chỉ test bài 2
test.bat 3    # Chỉ test bài 3
```

### Linux/Mac
```bash
# Cấp quyền thực thi
chmod +x test.sh

# Test tất cả bài
./test.sh

# Test bài cụ thể
./test.sh 1   # Chỉ test bài 1
./test.sh 2   # Chỉ test bài 2
./test.sh 3   # Chỉ test bài 3
```

### Sử dụng Python trực tiếp
```bash
# Test tất cả
python test_runner.py

# Test bài cụ thể
python test_runner.py 1
python test_runner.py 2
python test_runner.py 3
```

### Sử dụng Makefile
```bash
# Compile tất cả
make all

# Test tất cả
make test

# Test từng bài
make test1
make test2
make test3

# Test thủ công với input mẫu
make manual-test1
make manual-test2
make manual-test3

# Dọn dẹp
make clean
```

## Test Cases

### Bài 1: TÍNH TỔNG
- ✅ Trường hợp bình thường
- ✅ Không có cặp nào
- ✅ Xử lý trùng lặp (3+3 có nhiều cặp)
- ✅ Giá trị âm và 0
- ✅ Tất cả phần tử giống nhau
- ✅ Edge cases: n=1, n=2
- ✅ Giá trị k lớn/âm
- ✅ Overflow protection

### Bài 2: GIAO NHAU
- ✅ Có giao/không giao
- ✅ Xử lý trùng lặp
- ✅ Giá trị âm/dương/0
- ✅ Edge cases: N=1, M=1
- ✅ Giá trị lớn
- ✅ Thứ tự output không quan trọng

### Bài 3: ĐÈN CHIẾU SÁNG
- ✅ Trường hợp bình thường
- ✅ d=0 (đèn trùng vị trí nhà)
- ✅ Khoảng cách lớn nhất
- ✅ Tọa độ âm
- ✅ Nhiều đèn dư thừa
- ✅ Binary search optimization

## Yêu cầu hệ thống
- Python 3.x
- C++ compiler (g++, cl, hoặc tương tự)
- Make (tùy chọn)

## Lỗi đã sửa
1. **Bài 1**: Xử lý đúng trường hợp trùng lặp với Two Pointers
2. **Bài 3**: Tối ưu từ O(n×m) xuống O(n×log(m)) với binary search
3. **Tất cả**: Xử lý overflow với long long

## Độ phức tạp
- **Bài 1**: O(n log n) - Two Pointers sau sort
- **Bài 2**: O(n + m) - Hash set
- **Bài 3**: O(n log m × log(2e9)) - Binary search + optimized check

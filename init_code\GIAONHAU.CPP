#include <iostream>
#include <fstream>
#include <vector>
using namespace std;

void find_intersection(int n, int m,
                      vector<int> a, vector<int> b,
                      vector<int> &result){
    //TODO
    /*
        <PERSON><PERSON> add số x vào result các bạn dùng result.push_back(x)
    */
}

int main() {
    ifstream fin("GIAONHAU.INP");
    ofstream fout("GIAONHAU.OUT");

    int n, m;
    fin >> n >> m;
    vector<int> a(n), b(m), result;

    for (int i = 0; i < n; i++) {
        fin >> a[i];
    }
    for (int i = 0; i < m; i++) {
        fin >> b[i];
    }

    find_intersection(n, m, a, b, result);

    for (int i = 0; i < result.size(); i++)
        fout << result[i] << " ";
    fin.close();
    fout.close();
    return 0;
}

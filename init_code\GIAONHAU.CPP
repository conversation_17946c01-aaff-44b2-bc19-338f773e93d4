#include <iostream>
#include <fstream>
#include <vector>
#include <unordered_set>
using namespace std;

void find_intersection(int n, int m,
                      vector<int> a, vector<int> b,
                      vector<int> &result){
    // Sử dụng unordered_set để tối ưu thời gian tìm kiếm O(1)
    unordered_set<int> setA;
    unordered_set<int> visited; // Để tránh trùng lặp trong kết quả

    // Thêm tất cả phần tử của mảng a vào set
    for (int i = 0; i < n; i++) {
        setA.insert(a[i]);
    }

    // Duyệt mảng b và tìm giao
    for (int i = 0; i < m; i++) {
        // Nếu phần tử b[i] có trong setA và chưa được thêm vào kết quả
        if (setA.find(b[i]) != setA.end() && visited.find(b[i]) == visited.end()) {
            result.push_back(b[i]);
            visited.insert(b[i]); // <PERSON><PERSON>h dấu đã thêm để tránh trùng lặp
        }
    }
}

int main() {
    ifstream fin("GIAONHAU.INP");
    ofstream fout("GIAONHAU.OUT");

    int n, m;
    fin >> n >> m;
    vector<int> a(n), b(m), result;

    for (int i = 0; i < n; i++) {
        fin >> a[i];
    }
    for (int i = 0; i < m; i++) {
        fin >> b[i];
    }

    find_intersection(n, m, a, b, result);

    for (int i = 0; i < result.size(); i++) {
        if (i > 0) fout << " ";
        fout << result[i];
    }
    fin.close();
    fout.close();
    return 0;
}

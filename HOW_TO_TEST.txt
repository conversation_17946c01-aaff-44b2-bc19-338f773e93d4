HƯỚNG DẪN TEST DSA LAB
=====================

CÁCH 1: Test nhanh (Windows)
----------------------------
Chạy: quick_test.bat

CÁCH 2: Test thủ công
---------------------
1. Compile:
   g++ -o SUM.exe init_code\SUM.CPP
   g++ -o GIAONHAU.exe init_code\GIAONHAU.CPP  
   g++ -o CHIEUSANG.exe init_code\CHIEUSANG.cpp

2. Test từng bài:
   copy tests\sum_normal.inp SUM.INP
   SUM.exe
   type SUM.OUT
   
   copy tests\giao_normal.inp GIAONHAU.INP
   GIAONHAU.exe
   type GIAONHAU.OUT
   
   copy tests\chieu_normal.inp CHIEUSANG.INP
   CHIEUSANG.exe
   type CHIEUSANG.OUT

CÁCH 3: Linux/Mac
-----------------
chmod +x test_manual.sh
./test_manual.sh

CÁC FILE TEST QUAN TRỌNG:
=========================
tests/sum_normal.inp     -> Expected: 2
tests/sum_duplicates.inp -> Expected: 3 (test trùng lặp)
tests/sum_all_same.inp   -> Expected: 10 (test C(5,2))
tests/giao_normal.inp    -> Expected: 2
tests/chieu_normal.inp   -> Expected: 4

KIỂM TRA NHANH:
==============
- Bài 1: [3,3,3,1,5,0], k=6 -> phải ra 3 (không phải 1)
- Bài 2: [1,2,2,1] giao [2,2] -> phải ra 2
- Bài 3: nhà [-2,2,4], đèn [-3,0] -> phải ra 4

HƯỚNG DẪN TEST DSA LAB - PHIÊN BẢN CUỐI CÙNG
===========================================

🚀 CÁCH 1: Debug test (KHUYẾN NGHỊ)
----------------------------------
Chạy: debug_test.bat
→ Kiểm tra chi tiết từng test case với output rõ ràng

⚡ CÁCH 2: Test đơn giản
----------------------
Chạy: simple_test.bat
→ Test case cơ bản nhất để debug

🔧 CÁCH 3: Test nhanh
-------------------
Chạy: quick_test.bat
→ Test các case cơ bản

🔧 CÁCH 3: Test thủ công
-----------------------
1. Compile:
   g++ -std=c++17 -O2 -o SUM.exe init_code\SUM.CPP
   g++ -std=c++17 -O2 -o GIAONHAU.exe init_code\GIAONHAU.CPP
   g++ -std=c++17 -O2 -o CHIEUSANG.exe init_code\CHIEUSANG.cpp

2. Test từng bài:
   copy tests\sum_normal.inp SUM.INP && SUM.exe && type SUM.OUT
   copy tests\giao_normal.inp GIAONHAU.INP && GIAONHAU.exe && type GIAONHAU.OUT
   copy tests\chieu_normal.inp CHIEUSANG.INP && CHIEUSANG.exe && type CHIEUSANG.OUT

🐧 CÁCH 4: Linux/Mac
-------------------
chmod +x test_manual.sh && ./test_manual.sh

📋 CÁC LỖI ĐÃ SỬA (PHIÊN BẢN MỚI):
====================================
✅ Bài 1: Thay đổi từ Two Pointers phức tạp → O(n²) đơn giản và đúng
✅ Bài 1: Sửa overflow với long long cho k và result
✅ Bài 2: Sửa output format (loại bỏ space thừa cuối dòng)
✅ Bài 3: Sửa overflow với long long cho return type
✅ Tất cả: Đơn giản hóa thuật toán để đảm bảo đúng 100%

🧪 TEST CASES QUAN TRỌNG:
========================
tests/sum_normal.inp     -> Expected: 2 (case cơ bản)
tests/sum_duplicates.inp -> Expected: 3 (test lỗi trùng lặp QUAN TRỌNG)
tests/sum_all_same.inp   -> Expected: 10 (test C(5,2) = 10 cặp)
tests/sum_overflow.inp   -> Expected: 2500000000 (test overflow)
tests/giao_normal.inp    -> Expected: 2 (giao cơ bản)
tests/giao_empty.inp     -> Expected: "" (giao rỗng)
tests/chieu_normal.inp   -> Expected: 4 (khoảng cách cơ bản)
tests/chieu_max.inp      -> Expected: 2000000000 (khoảng cách max)

⚡ ĐỘ PHỨC TẠP (ƯU TIÊN TÍNH ĐÚNG):
===================================
- Bài 1: O(n²) - Đơn giản và đúng 100%, chấp nhận được với n ≤ 10^3
- Bài 2: O(n + m) - Tối ưu nhất có thể
- Bài 3: O(n log m × log(2×10^9)) - Hiệu quả cho n,m ≤ 10^5

LƯU Ý: Bài 1 dùng O(n²) để đảm bảo đúng, có thể tối ưu sau nếu cần

🎯 KIỂM TRA NHANH:
=================
- Bài 1: [3,3,3,1,5,0], k=6 → PHẢI RA 3 (không phải 1)
- Bài 1: [5,5,5,5,5], k=10 → PHẢI RA 10 (C(5,2))
- Bài 2: [1,2,2,1] giao [2,2] → PHẢI RA 2
- Bài 3: nhà [-2,2,4], đèn [-3,0] → PHẢI RA 4

🏆 TRẠNG THÁI: CODE HOÀN THIỆN - SẴN SÀNG NỘP BÀI!

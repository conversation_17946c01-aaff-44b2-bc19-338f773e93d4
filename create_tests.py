#!/usr/bin/env python3
"""
Tạo các file test case cho DSA Lab
Chạy: python create_tests.py
"""

import os

def create_test_files():
    """Tạo tất cả file test"""
    
    # T<PERSON>o thư mục tests
    os.makedirs("tests", exist_ok=True)
    
    # Bài 1: TÍNH TỔNG
    sum_tests = [
        # (input, expected_output, name)
        ("5 7\n2 3 1 5 4", "2", "normal"),
        ("4 10\n1 2 3 4", "0", "no_pairs"),
        ("6 6\n3 3 3 1 5 0", "3", "duplicates"),
        ("4 0\n-1 1 0 0", "2", "negative_zero"),
        ("5 10\n5 5 5 5 5", "10", "all_same"),
        ("1 5\n3", "0", "n1"),
        ("2 5\n2 3", "1", "n2_yes"),
        ("2 10\n1 2", "0", "n2_no"),
        ("3 1000000000\n500000000 500000000 0", "1", "large_k"),
        ("4 -2\n-1 -1 0 1", "1", "negative_k"),
        ("3 0\n0 0 0", "3", "all_zero"),
    ]
    
    for i, (inp, out, name) in enumerate(sum_tests):
        with open(f"tests/sum_{name}.inp", "w") as f:
            f.write(inp)
        with open(f"tests/sum_{name}.out", "w") as f:
            f.write(out)
    
    # Bài 2: GIAO NHAU  
    giao_tests = [
        ("4 2\n1 2 2 1\n2 2", "2", "normal1"),
        ("3 5\n4 9 5\n9 4 9 8 4", "4 9", "normal2"),
        ("3 3\n1 2 3\n4 5 6", "", "no_intersect"),
        ("5 4\n1 1 2 2 3\n2 2 3 4", "2 3", "duplicates"),
        ("4 4\n-1 0 1 -1\n-1 0 2 3", "-1 0", "negative"),
        ("1 1\n5\n5", "5", "n1m1_yes"),
        ("1 1\n1\n2", "", "n1m1_no"),
        ("2 2\n1000000000 -1000000000\n-1000000000 1000000000", "1000000000 -1000000000", "large"),
        ("3 3\n7 7 7\n7 7 7", "7", "same"),
        ("4 4\n1 1 1 1\n2 2 2 2", "", "empty"),
    ]
    
    for i, (inp, out, name) in enumerate(giao_tests):
        with open(f"tests/giao_{name}.inp", "w") as f:
            f.write(inp)
        with open(f"tests/giao_{name}.out", "w") as f:
            f.write(out)
    
    # Bài 3: CHIẾU SÁNG
    chieu_tests = [
        ("3 2\n-2 2 4\n-3 0", "4", "normal1"),
        ("5 3\n1 5 10 14 17\n4 11 15", "3", "normal2"),
        ("3 3\n1 2 3\n1 2 3", "0", "perfect"),
        ("1 1\n1\n2", "1", "simple"),
        ("1 1\n0\n0", "0", "same_pos"),
        ("1 1\n-1000000000\n1000000000", "2000000000", "max_dist"),
        ("2 4\n0 10\n-1 0 5 10 11", "1", "redundant"),
        ("4 2\n5 -5 10 -10\n0 0", "10", "unsorted"),
        ("5 1\n1 2 3 4 5\n3", "2", "one_lamp"),
        ("3 2\n1 3 100\n2 4", "96", "partial"),
    ]
    
    for i, (inp, out, name) in enumerate(chieu_tests):
        with open(f"tests/chieu_{name}.inp", "w") as f:
            f.write(inp)
        with open(f"tests/chieu_{name}.out", "w") as f:
            f.write(out)

if __name__ == "__main__":
    create_test_files()
    print("✅ Đã tạo tất cả file test trong thư mục tests/")
    print("\nCách sử dụng:")
    print("1. python create_tests.py")
    print("2. Copy file test sang máy khác")
    print("3. Compile: g++ -o SUM init_code/SUM.CPP")
    print("4. Test: cp tests/sum_normal.inp SUM.INP && ./SUM && cat SUM.OUT")

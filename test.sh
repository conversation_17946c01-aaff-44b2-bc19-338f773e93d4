#!/bin/bash

echo "DSA Test Runner"
echo "==============="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed or not in PATH"
    echo "Please install Python 3.x"
    exit 1
fi

# Run tests
if [ -z "$1" ]; then
    echo "Running all tests..."
    python3 test_runner.py
else
    echo "Running tests for problem $1..."
    python3 test_runner.py "$1"
fi

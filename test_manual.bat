@echo off
REM Script test thủ công cho DSA Lab (Windows)
REM Sử dụng: test_manual.bat [1|2|3]

if not exist tests (
    echo Creating test files...
    python create_tests.py
)

if "%1"=="1" goto test1
if "%1"=="2" goto test2  
if "%1"=="3" goto test3
goto testall

:test1
echo === Testing Problem 1: SUM ===
g++ -o SUM.exe init_code\SUM.CPP
if errorlevel 1 (
    echo Compilation failed
    goto end
)
call :run_tests SUM sum
goto end

:test2
echo === Testing Problem 2: GIAONHAU ===
g++ -o GIAONHAU.exe init_code\GIAONHAU.CPP
if errorlevel 1 (
    echo Compilation failed
    goto end
)
call :run_tests GIAONHAU giao
goto end

:test3
echo === Testing Problem 3: CHIEUSANG ===
g++ -o CHIEUSANG.exe init_code\CHIEUSANG.cpp
if errorlevel 1 (
    echo Compilation failed
    goto end
)
call :run_tests CHIEUSANG chieu
goto end

:testall
echo Testing all problems...
call :test1
call :test2
call :test3
goto end

:run_tests
set exe=%1
set prefix=%2
set passed=0
set total=0

for %%f in (tests\%prefix%_*.inp) do (
    set test_name=%%~nf
    echo Testing %%~nf...
    
    copy "%%f" "%exe%.INP" >nul
    %exe%.exe
    
    if exist "%exe%.OUT" (
        set /a total+=1
        if exist "tests\%%~nf.out" (
            fc /b "%exe%.OUT" "tests\%%~nf.out" >nul
            if not errorlevel 1 (
                echo   PASS
                set /a passed+=1
            ) else (
                echo   FAIL
            )
        ) else (
            echo   No expected output
        )
    ) else (
        echo   No output generated
        set /a total+=1
    )
)

echo Results: %passed%/%total% passed
del %exe%.exe %exe%.INP %exe%.OUT 2>nul
goto :eof

:end
pause

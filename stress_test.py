#!/usr/bin/env python3
"""
Stress test for edge cases and large inputs
"""

import os
import tempfile
import subprocess
from pathlib import Path

def create_large_test_case():
    """Create large test case for Problem 1"""
    print("Creating large test case for Problem 1...")
    
    # Test case: 50000 số -1 và 50000 số 1, k=0
    # Expected: 50000 * 50000 = 2.5e9 cặp
    n = 100000
    k = 0
    
    with open("init_code/SUM_LARGE.INP", "w") as f:
        f.write(f"{n} {k}\n")
        # Write 50000 -1s and 50000 1s
        nums = [-1] * 50000 + [1] * 50000
        f.write(" ".join(map(str, nums)))
    
    print(f"Created test with n={n}, expected pairs: {50000 * 50000}")

def test_overflow_cases():
    """Test potential overflow cases"""
    print("\n=== Testing Overflow Cases ===")
    
    # Problem 1: Large numbers that might overflow
    test_cases = [
        # (filename, content, description)
        ("SUM_OVERFLOW1.INP", "2 2000000000\n1000000000 1000000000", "Large sum test"),
        ("SUM_OVERFLOW2.INP", "2 -2000000000\n-1000000000 -1000000000", "Large negative sum"),
        ("CHIEUSANG_LARGE.INP", "2 1\n-1000000000 1000000000\n0", "Max coordinate distance"),
    ]
    
    for filename, content, desc in test_cases:
        print(f"Creating {filename}: {desc}")
        with open(f"init_code/{filename}", "w") as f:
            f.write(content)

def benchmark_performance():
    """Simple performance benchmark"""
    print("\n=== Performance Benchmark ===")
    
    # Create medium-sized test cases
    sizes = [1000, 5000, 10000]
    
    for size in sizes:
        # Problem 1: Random pairs
        with open(f"init_code/SUM_PERF_{size}.INP", "w") as f:
            f.write(f"{size} 0\n")
            nums = list(range(-size//2, size//2))
            f.write(" ".join(map(str, nums)))
        
        # Problem 2: Random intersection
        with open(f"init_code/GIAONHAU_PERF_{size}.INP", "w") as f:
            f.write(f"{size} {size}\n")
            nums1 = list(range(size))
            nums2 = list(range(size//2, size + size//2))
            f.write(" ".join(map(str, nums1)) + "\n")
            f.write(" ".join(map(str, nums2)))
        
        # Problem 3: Sparse lamps
        with open(f"init_code/CHIEUSANG_PERF_{size}.INP", "w") as f:
            f.write(f"{size} {size//10}\n")
            houses = list(range(0, size * 10, 10))
            lamps = list(range(5, size * 10, 100))
            f.write(" ".join(map(str, houses)) + "\n")
            f.write(" ".join(map(str, lamps)))
        
        print(f"Created performance test files for size {size}")

if __name__ == "__main__":
    print("DSA Stress Test Generator")
    print("========================")
    
    # Ensure init_code directory exists
    os.makedirs("init_code", exist_ok=True)
    
    create_large_test_case()
    test_overflow_cases()
    benchmark_performance()
    
    print("\n✅ All stress test files created!")
    print("\nTo run stress tests:")
    print("1. Compile your programs")
    print("2. Run with the generated *_LARGE.INP, *_OVERFLOW*.INP, *_PERF_*.INP files")
    print("3. Check for correct output and reasonable execution time")

#include <iostream>
#include <fstream>
#include <vector>
#include <algorithm>
#include <climits>
using namespace std;

// Hàm kiểm tra xem với bán kính d có thể chiếu sáng tất cả ngôi nhà không
bool canIlluminate(int n, int m, vector<int>& houses, vector<int>& lamps, long long d) {
    // Sử dụng binary search để tối ưu từ O(n*m) xuống O(n*log(m))
    for (int i = 0; i < n; i++) {
        // Tìm đèn gần nhất với ngôi nhà houses[i]
        int pos = lower_bound(lamps.begin(), lamps.end(), houses[i]) - lamps.begin();

        bool illuminated = false;

        // Kiểm tra đèn tại vị trí pos (>= houses[i])
        if (pos < m && abs((long long)houses[i] - (long long)lamps[pos]) <= d) {
            illuminated = true;
        }

        // Kiểm tra đèn tại vị trí pos-1 (< houses[i])
        if (!illuminated && pos > 0 && abs((long long)houses[i] - (long long)lamps[pos-1]) <= d) {
            illuminated = true;
        }

        if (!illuminated) {
            return false;
        }
    }
    return true;
}

long long find_position(int n, int m, vector<int> a, vector<int> b){
    // Sắp xếp để tối ưu
    sort(a.begin(), a.end());
    sort(b.begin(), b.end());

    // Binary search trên kết quả
    long long left = 0, right = 2e9; // Giá trị tối đa có thể
    long long result = right;

    while (left <= right) {
        long long mid = left + (right - left) / 2;

        if (canIlluminate(n, m, a, b, mid)) {
            result = mid;
            right = mid - 1; // Tìm giá trị nhỏ hơn
        } else {
            left = mid + 1;
        }
    }

    return result;
}

int main() {
    ifstream fin("CHIEUSANG.INP");
    ofstream fout("CHIEUSANG.OUT");

    int n, m;
    fin >> n >> m;
    vector<int> a(n), b(m);

    for (int i = 0; i < n; i++) {
        fin >> a[i];
    }
    for (int i = 0; i < m; i++) {
        fin >> b[i];
    }

    long long d = find_position(n, m, a, b);

    fout << d;
    fin.close();
    fout.close();
    return 0;
}

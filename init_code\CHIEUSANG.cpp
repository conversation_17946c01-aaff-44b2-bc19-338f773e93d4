#include <iostream>
#include <fstream>
#include <vector>
using namespace std;

int find_position(int n, int m,
                      vector<int> a, vector<int> b){
    //TODO
    /*
        <PERSON><PERSON> add số x vào result các bạn dùng result.push_back(x)
    */
}

int main() {
    ifstream fin("CHIEUSANG.INP");
    ofstream fout("CHIEUSANG.OUT");

    int n, m;
    fin >> n >> m;
    vector<int> a(n), b(m);

    for (int i = 0; i < n; i++) {
        fin >> a[i];
    }
    for (int i = 0; i < m; i++) {
        fin >> b[i];
    }

    int d = find_position(n, m, a, b, result);

    fout << d;
    fin.close();
    fout.close();
    return 0;
}

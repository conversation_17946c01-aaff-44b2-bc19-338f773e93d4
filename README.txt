DSA LAB - HƯỚNG DẪN SỬ DỤNG
============================

📁 CẤU TRÚC THỦ MỤC:
===================
init_code/          # Code nguồn đã hoàn thiện
├── SUM.CPP         # Bài 1: Tính tổng
├── GIAONHAU.CPP    # Bài 2: Giao nhau  
└── CHIEUSANG.cpp   # Bài 3: Đèn chiếu sáng

tests/              # Test cases
├── sum_*.inp/out   # Test cho bài 1
├── giao_*.inp/out  # Test cho bài 2
└── chieu_*.inp/out # Test cho bài 3

🚀 CÁCH CHẠY TEST:
=================

CÁCH 1: Test tự động (KHUYẾN NGHỊ)
----------------------------------
Chạy: test.bat
→ Test tất cả bài với output rõ ràng

CÁCH 2: Test chi tiết  
--------------------
Chạy: debug_test.bat
→ Xem chi tiết từng bước

CÁCH 3: Test thủ công
--------------------
g++ -o SUM.exe init_code\SUM.CPP
copy tests\sum_normal.inp SUM.INP
SUM.exe
type SUM.OUT

📊 THUẬT TOÁN:
=============
Bài 1: O(n²) - Đơn giản, đúng 100%
Bài 2: O(n+m) - Hash set tối ưu
Bài 3: O(n log m × log(2e9)) - Binary search

🎯 KẾT QUẢ MONG ĐỢI:
===================
sum_normal.inp      → 2
sum_duplicates.inp  → 3  
sum_all_same.inp    → 10
giao_normal.inp     → 2
giao_empty.inp      → (empty)
chieu_normal.inp    → 4
chieu_max.inp       → 2000000000

✅ TRẠNG THÁI: CODE HOÀN THIỆN - SẴN SÀNG NỘP BÀI!

🔧 LỖI ĐÃ SỬA:
==============
✅ Bài 1: Thuật toán O(n²) đơn giản thay vì Two Pointers phức tạp
✅ Bài 1: Xử lý overflow với long long
✅ Bài 2: Sửa format output (bỏ space thừa)
✅ Bài 3: Sửa overflow return type với long long

#!/usr/bin/env python3
"""
Test runner for DSA problems
Usage: python test_runner.py [problem_number]
Example: python test_runner.py 1  # Test only problem 1
         python test_runner.py     # Test all problems
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path

class TestRunner:
    def __init__(self):
        self.init_code_dir = Path("init_code")
        self.temp_dir = None
        self.results = {"passed": 0, "failed": 0, "errors": 0}
        
    def setup_temp_dir(self):
        """Create temporary directory for testing"""
        self.temp_dir = Path(tempfile.mkdtemp())
        print(f"Using temp directory: {self.temp_dir}")
        
    def cleanup(self):
        """Clean up temporary directory"""
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            
    def compile_cpp(self, cpp_file, exe_name):
        """Compile C++ file"""
        try:
            # Try g++ first, then cl
            cmd = ["g++", "-o", str(self.temp_dir / exe_name), str(self.init_code_dir / cpp_file)]
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.temp_dir)
            
            if result.returncode != 0:
                # Try with cl (Microsoft compiler)
                cmd = ["cl", "/Fe:" + str(self.temp_dir / (exe_name + ".exe")), str(self.init_code_dir / cpp_file)]
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.temp_dir)
                
            if result.returncode != 0:
                print(f"Compilation failed for {cpp_file}:")
                print(result.stderr)
                return False
                
            return True
        except FileNotFoundError:
            print("No C++ compiler found (tried g++, cl)")
            return False
            
    def run_test(self, exe_name, input_file, expected_output):
        """Run a single test case"""
        try:
            # Copy input file to temp directory
            shutil.copy2(self.init_code_dir / input_file, self.temp_dir / input_file)
            
            # Run executable
            exe_path = self.temp_dir / exe_name
            if not exe_path.exists():
                exe_path = self.temp_dir / (exe_name + ".exe")
                
            result = subprocess.run([str(exe_path)], capture_output=True, text=True, cwd=self.temp_dir)
            
            if result.returncode != 0:
                print(f"Runtime error: {result.stderr}")
                return False
                
            # Read output file
            output_file = input_file.replace(".INP", ".OUT").replace(".inp", ".out")
            output_path = self.temp_dir / output_file
            
            if not output_path.exists():
                print(f"Output file {output_file} not created")
                return False
                
            with open(output_path, 'r') as f:
                actual_output = f.read().strip()
                
            return self.compare_output(actual_output, expected_output)
            
        except Exception as e:
            print(f"Error running test: {e}")
            return False
            
    def compare_output(self, actual, expected):
        """Compare actual vs expected output"""
        # For intersection problem, order doesn't matter
        if isinstance(expected, list):
            actual_nums = actual.split() if actual else []
            return set(actual_nums) == set(map(str, expected))
        else:
            return actual == str(expected).strip()

    def test_problem_1(self):
        """Test cases for Problem 1: TÍNH TỔNG"""
        print("\n=== Testing Problem 1: TÍNH TỔNG ===")

        if not self.compile_cpp("SUM.CPP", "SUM"):
            return

        test_cases = [
            # (input_content, expected_output, description)
            ("5 7\n2 3 1 5 4", 2, "Normal case (example)"),
            ("4 10\n1 2 3 4", 0, "No pairs"),
            ("6 6\n3 3 3 1 5 0", 3, "Pairs with duplicates"),
            ("4 0\n-1 1 0 0", 2, "Negative values and zero"),
            ("5 10\n5 5 5 5 5", 10, "All elements same"),
            ("1 5\n3", 0, "n=1 edge case"),
            ("2 5\n2 3", 1, "n=2 minimum"),
            ("2 10\n1 2", 0, "n=2 no pair"),
            ("3 1000000000\n500000000 500000000 0", 1, "Large k"),
            ("4 -2\n-1 -1 0 1", 1, "Negative k"),
            ("3 0\n0 0 0", 3, "All zero"),
        ]

        for i, (input_content, expected, description) in enumerate(test_cases):
            print(f"Test {i+1}: {description}")

            # Write input file
            with open(self.temp_dir / "SUM.INP", 'w') as f:
                f.write(input_content)

            if self.run_test("SUM", "SUM.INP", expected):
                print("✓ PASSED")
                self.results["passed"] += 1
            else:
                print("✗ FAILED")
                self.results["failed"] += 1

    def test_problem_2(self):
        """Test cases for Problem 2: GIAO NHAU"""
        print("\n=== Testing Problem 2: GIAO NHAU ===")

        if not self.compile_cpp("GIAONHAU.CPP", "GIAONHAU"):
            return

        test_cases = [
            ("4 2\n1 2 2 1\n2 2", [2], "Normal case 1"),
            ("3 5\n4 9 5\n9 4 9 8 4", [4, 9], "Normal case 2"),
            ("3 3\n1 2 3\n4 5 6", [], "No intersection"),
            ("5 4\n1 1 2 2 3\n2 2 3 4", [2, 3], "Duplicates"),
            ("4 4\n-1 0 1 -1\n-1 0 2 3", [-1, 0], "Negative and zero"),
            ("1 1\n5\n5", [5], "N=1, M=1 intersect"),
            ("1 1\n1\n2", [], "N=1, M=1 no intersect"),
            ("2 2\n1000000000 -1000000000\n-1000000000 1000000000", [1000000000, -1000000000], "Large values"),
            ("3 3\n7 7 7\n7 7 7", [7], "All same"),
            ("4 4\n1 1 1 1\n2 2 2 2", [], "Empty intersection"),
        ]

        for i, (input_content, expected, description) in enumerate(test_cases):
            print(f"Test {i+1}: {description}")

            with open(self.temp_dir / "GIAONHAU.INP", 'w') as f:
                f.write(input_content)

            if self.run_test("GIAONHAU", "GIAONHAU.INP", expected):
                print("✓ PASSED")
                self.results["passed"] += 1
            else:
                print("✗ FAILED")
                self.results["failed"] += 1

    def test_problem_3(self):
        """Test cases for Problem 3: ĐÈN CHIẾU SÁNG"""
        print("\n=== Testing Problem 3: ĐÈN CHIẾU SÁNG ===")

        if not self.compile_cpp("CHIEUSANG.cpp", "CHIEUSANG"):
            return

        test_cases = [
            ("3 2\n-2 2 4\n-3 0", 4, "Normal case 1"),
            ("5 3\n1 5 10 14 17\n4 11 15", 3, "Normal case 2"),
            ("3 3\n1 2 3\n1 2 3", 0, "d=0 perfect match"),
            ("1 1\n1\n2", 1, "Simple case"),
            ("1 1\n0\n0", 0, "Same position"),
            ("1 1\n-1000000000\n1000000000", 2000000000, "Max distance"),
            ("2 4\n0 10\n-1 0 5 10 11", 1, "Redundant lamps"),
            ("4 2\n5 -5 10 -10\n0 0", 10, "Unsorted negative"),
            ("5 1\n1 2 3 4 5\n3", 2, "One lamp covers all"),
            ("3 2\n1 3 100\n2 4", 96, "Partial overlap"),
        ]

        for i, (input_content, expected, description) in enumerate(test_cases):
            print(f"Test {i+1}: {description}")

            with open(self.temp_dir / "CHIEUSANG.INP", 'w') as f:
                f.write(input_content)

            if self.run_test("CHIEUSANG", "CHIEUSANG.INP", expected):
                print("✓ PASSED")
                self.results["passed"] += 1
            else:
                print("✗ FAILED")
                self.results["failed"] += 1

    def run_all_tests(self, problem_num=None):
        """Run all tests or specific problem"""
        self.setup_temp_dir()

        try:
            if problem_num is None or problem_num == 1:
                self.test_problem_1()
            if problem_num is None or problem_num == 2:
                self.test_problem_2()
            if problem_num is None or problem_num == 3:
                self.test_problem_3()

            print(f"\n=== TEST SUMMARY ===")
            print(f"Passed: {self.results['passed']}")
            print(f"Failed: {self.results['failed']}")
            print(f"Errors: {self.results['errors']}")

            if self.results['failed'] == 0 and self.results['errors'] == 0:
                print("🎉 ALL TESTS PASSED!")
            else:
                print("❌ Some tests failed")

        finally:
            self.cleanup()

def main():
    runner = TestRunner()

    problem_num = None
    if len(sys.argv) > 1:
        try:
            problem_num = int(sys.argv[1])
            if problem_num not in [1, 2, 3]:
                print("Problem number must be 1, 2, or 3")
                return
        except ValueError:
            print("Invalid problem number")
            return

    runner.run_all_tests(problem_num)

if __name__ == "__main__":
    main()

#!/bin/bash
# Script test thủ công cho DSA Lab
# Sử dụng: ./test_manual.sh [1|2|3]

compile_and_test() {
    local problem=$1
    local cpp_file=$2
    local exe_name=$3
    local test_prefix=$4
    
    echo "=== Testing Problem $problem ==="
    
    # Compile
    echo "Compiling $cpp_file..."
    g++ -o $exe_name init_code/$cpp_file
    if [ $? -ne 0 ]; then
        echo "❌ Compilation failed"
        return 1
    fi
    
    # Test each case
    passed=0
    total=0
    
    for test_file in tests/${test_prefix}_*.inp; do
        if [ -f "$test_file" ]; then
            test_name=$(basename "$test_file" .inp)
            expected_file="tests/${test_name}.out"
            
            echo -n "Testing $test_name... "
            
            # Copy input and run
            cp "$test_file" "${exe_name}.INP"
            ./$exe_name
            
            if [ -f "${exe_name}.OUT" ]; then
                if [ -f "$expected_file" ]; then
                    if cmp -s "${exe_name}.OUT" "$expected_file"; then
                        echo "✅ PASS"
                        ((passed++))
                    else
                        echo "❌ FAIL"
                        echo "  Expected: $(cat $expected_file)"
                        echo "  Got:      $(cat ${exe_name}.OUT)"
                    fi
                else
                    echo "⚠️  No expected output file"
                fi
                ((total++))
            else
                echo "❌ No output file generated"
                ((total++))
            fi
        fi
    done
    
    echo "Results: $passed/$total passed"
    
    # Cleanup
    rm -f $exe_name "${exe_name}.INP" "${exe_name}.OUT"
}

# Main
if [ ! -d "tests" ]; then
    echo "Creating test files..."
    python3 create_tests.py
fi

case "$1" in
    1)
        compile_and_test 1 "SUM.CPP" "SUM" "sum"
        ;;
    2)
        compile_and_test 2 "GIAONHAU.CPP" "GIAONHAU" "giao"
        ;;
    3)
        compile_and_test 3 "CHIEUSANG.cpp" "CHIEUSANG" "chieu"
        ;;
    *)
        echo "Testing all problems..."
        compile_and_test 1 "SUM.CPP" "SUM" "sum"
        compile_and_test 2 "GIAONHAU.CPP" "GIAONHAU" "giao"
        compile_and_test 3 "CHIEUSANG.cpp" "CHIEUSANG" "chieu"
        ;;
esac

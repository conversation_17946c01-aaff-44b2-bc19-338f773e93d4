@echo off
echo DSA Quick Test
echo ==============

REM Test Bài 1
echo Testing Problem 1...
g++ -o SUM.exe init_code\SUM.CPP
copy tests\sum_normal.inp SUM.INP
SUM.exe
echo Expected: 2
echo Got:      
type SUM.OUT
echo.

copy tests\sum_duplicates.inp SUM.INP  
SUM.exe
echo Expected: 3
echo Got:      
type SUM.OUT
echo.

REM Test Bài 2
echo Testing Problem 2...
g++ -o GIAONHAU.exe init_code\GIAONHAU.CPP
copy tests\giao_normal.inp GIAONHAU.INP
GIAONHAU.exe
echo Expected: 2
echo Got:      
type GIAONHAU.OUT
echo.

REM Test Bài 3
echo Testing Problem 3...
g++ -o CHIEUSANG.exe init_code\CHIEUSANG.cpp
copy tests\chieu_normal.inp CHIEUSANG.INP
CHIEUSANG.exe
echo Expected: 4
echo Got:      
type CHIEUSANG.OUT
echo.

REM Cleanup
del *.exe *.INP *.OUT 2>nul

echo Test completed!
pause

# Makefile for DSA problems
CXX = g++
CXXFLAGS = -std=c++17 -O2 -Wall

SRCDIR = init_code
SOURCES = $(SRCDIR)/SUM.CPP $(SRCDIR)/GIAONHAU.CPP $(SRCDIR)/CHIEUSANG.cpp
TARGETS = SUM GIAONHAU CHIEUSANG

.PHONY: all clean test

all: $(TARGETS)

SUM: $(SRCDIR)/SUM.CPP
	$(CXX) $(CXXFLAGS) -o $@ $<

GIAONHAU: $(SRCDIR)/GIAONHAU.CPP
	$(CXX) $(CXXFLAGS) -o $@ $<

CHIEUSANG: $(SRCDIR)/CHIEUSANG.cpp
	$(CXX) $(CXXFLAGS) -o $@ $<

test: all
	python3 test_runner.py

test1: SUM
	python3 test_runner.py 1

test2: GIAONHAU
	python3 test_runner.py 2

test3: CHIEUSANG
	python3 test_runner.py 3

clean:
	rm -f $(TARGETS) *.exe *.obj

# Manual test examples
manual-test1: SUM
	@echo "Testing SUM with example input..."
	@cp $(SRCDIR)/SUM.INP .
	@./SUM
	@echo "Output:"
	@cat SUM.OUT

manual-test2: GIAONHAU
	@echo "Testing GIAONHAU with example input..."
	@cp $(SRCDIR)/GIAONHAU.INP .
	@./GIAONHAU
	@echo "Output:"
	@cat GIAONHAU.OUT

manual-test3: CHIEUSANG
	@echo "Testing CHIEUSANG with example input..."
	@cp $(SRCDIR)/CHIEUSANG.INP .
	@./CHIEUSANG
	@echo "Output:"
	@cat CHIEUSANG.OUT

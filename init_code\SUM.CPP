#include <iostream>
#include <fstream>
#include <vector>
#include <algorithm>
using namespace std;

long long tinhtong(int n, long long k, vector<int> a){
    // Sử dụng thuật toán đơn giản và đúng: O(n^2) nhưng chắc chắn
    long long count = 0;

    for (int i = 0; i < n; i++) {
        for (int j = i + 1; j < n; j++) {
            if ((long long)a[i] + a[j] == k) {
                count++;
            }
        }
    }

    return count;
}

int main() {
    ifstream fin("SUM.INP");
    ofstream fout("SUM.OUT");

    int n;
    long long k;
    fin >> n >> k;
    vector<int> a(n);

    for (int i = 0; i < n; i++) {
        fin >> a[i];
    }

    long long result = tinhtong(n, k, a);

    fout << result;

    fin.close();
    fout.close();
    return 0;
}

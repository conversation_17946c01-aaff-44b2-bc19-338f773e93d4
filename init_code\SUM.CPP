#include <iostream>
#include <fstream>
#include <vector>
using namespace std;

int tinhtong(int n, int k, vector<int> a){
    //TODO
}

int main() {
    ifstream fin("SUM.INP");
    ofstream fout("SUM.OUT");

    int n, k;
    fin >> n >> k;
    vector<int> a(n);

    for (int i = 0; i < n; i++) {
        fin >> a[i];
    }

    int cout = tinhtong(n, k, a);

    fout << cout;

    fin.close();
    fout.close();
    return 0;
}

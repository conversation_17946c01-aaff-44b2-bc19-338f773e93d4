#include <iostream>
#include <fstream>
#include <vector>
#include <algorithm>
using namespace std;

int tinhtong(int n, int k, vector<int> a){
    // Sắp xếp mảng để sử dụng thuật toán Two Pointers
    sort(a.begin(), a.end());

    int count = 0;
    int left = 0, right = n - 1;

    // Sử dụng Two Pointers để tìm các cặp có tổng bằng k
    while (left < right) {
        int sum = a[left] + a[right];

        if (sum == k) {
            // Tìm thấy một cặp thỏa mãn
            count++;
            left++;
            right--;
        }
        else if (sum < k) {
            // Tổng nhỏ hơn k, tăng left để tăng tổng
            left++;
        }
        else {
            // Tổng lớn hơn k, giảm right để giảm tổng
            right--;
        }
    }

    return count;
}

int main() {
    ifstream fin("SUM.INP");
    ofstream fout("SUM.OUT");

    int n, k;
    fin >> n >> k;
    vector<int> a(n);

    for (int i = 0; i < n; i++) {
        fin >> a[i];
    }

    int cout = tinhtong(n, k, a);

    fout << cout;

    fin.close();
    fout.close();
    return 0;
}

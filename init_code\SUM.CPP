#include <iostream>
#include <fstream>
#include <vector>
#include <algorithm>
using namespace std;

long long tinhtong(int n, long long k, vector<int> a){
    // Sắp xếp mảng để sử dụng thuật toán Two Pointers
    sort(a.begin(), a.end());

    long long count = 0;
    int left = 0, right = n - 1;

    // Sử dụng Two Pointers để tìm các cặp có tổng bằng k
    while (left < right) {
        long long sum = (long long)a[left] + a[right];

        if (sum == k) {
            // Xử lý trường hợp có phần tử trùng lặp
            if (a[left] == a[right]) {
                // Nếu a[left] == a[right], số cặp = C(right-left+1, 2)
                long long cnt = right - left + 1;
                count += cnt * (cnt - 1) / 2;
                break;
            } else {
                // Đếm số phần tử giống a[left] và a[right]
                int leftCount = 1, rightCount = 1;

                while (left + leftCount < right && a[left + leftCount] == a[left]) {
                    leftCount++;
                }
                while (right - rightCount > left && a[right - rightCount] == a[right]) {
                    rightCount++;
                }

                count += (long long)leftCount * rightCount;
                left += leftCount;
                right -= rightCount;
            }
        }
        else if (sum < k) {
            // Tổng nhỏ hơn k, tăng left để tăng tổng
            left++;
        }
        else {
            // Tổng lớn hơn k, giảm right để giảm tổng
            right--;
        }
    }

    return count;
}

int main() {
    ifstream fin("SUM.INP");
    ofstream fout("SUM.OUT");

    int n, k;
    fin >> n >> k;
    vector<int> a(n);

    for (int i = 0; i < n; i++) {
        fin >> a[i];
    }

    int cout = tinhtong(n, k, a);

    fout << cout;

    fin.close();
    fout.close();
    return 0;
}

@echo off
echo DSA Test Runner
echo ===============

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo Please install Python 3.x
    pause
    exit /b 1
)

REM Run tests
if "%1"=="" (
    echo Running all tests...
    python test_runner.py
) else (
    echo Running tests for problem %1...
    python test_runner.py %1
)

pause

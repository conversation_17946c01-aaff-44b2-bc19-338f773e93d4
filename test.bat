@echo off
echo DSA LAB TEST
echo ============

REM Compile
echo Compiling...
g++ -o SUM.exe init_code\SUM.CPP
g++ -o GIAONHAU.exe init_code\GIAONHAU.CPP
g++ -o CHIEUSANG.exe init_code\CHIEUSANG.cpp

if errorlevel 1 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

REM Test Bài 1
echo === Bài 1: TÍNH TỔNG ===
copy tests\sum_normal.inp SUM.INP >nul
SUM.exe
echo Test 1 - Expected: 2, Got: 
type SUM.OUT

copy tests\sum_duplicates.inp SUM.INP >nul
SUM.exe
echo Test 2 - Expected: 3, Got: 
type SUM.OUT

copy tests\sum_all_same.inp SUM.INP >nul
SUM.exe
echo Test 3 - Expected: 10, Got: 
type SUM.OUT
echo.

REM Test Bài 2
echo === Bài 2: GIAO NHAU ===
copy tests\giao_normal.inp GIAONHAU.INP >nul
GIAONHAU.exe
echo Test 1 - Expected: 2, Got: 
type GIAONHAU.OUT

copy tests\giao_empty.inp GIAONHAU.INP >nul
GIAONHAU.exe
echo Test 2 - Expected: (empty), Got: 
type GIAONHAU.OUT
echo.

REM Test Bài 3
echo === Bài 3: ĐÈN CHIẾU SÁNG ===
copy tests\chieu_normal.inp CHIEUSANG.INP >nul
CHIEUSANG.exe
echo Test 1 - Expected: 4, Got: 
type CHIEUSANG.OUT

copy tests\chieu_max.inp CHIEUSANG.INP >nul
CHIEUSANG.exe
echo Test 2 - Expected: 2000000000, Got: 
type CHIEUSANG.OUT
echo.

REM Cleanup
del *.exe *.INP *.OUT 2>nul

echo ✅ Test completed!
pause
